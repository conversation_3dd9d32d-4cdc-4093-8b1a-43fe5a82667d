<template>
  <div class="test-page">
    <h1>前台测试页面</h1>
    <p>这是一个简单的测试页面，用于验证路由是否正常工作。</p>
    <p>当前时间：{{ currentTime }}</p>

    <el-button type="primary" @click="handleClick">
      点击测试
    </el-button>

    <div v-if="clicked" class="success-message">
      <p>✅ 页面正常工作！</p>
      <p>Vue响应式系统正常</p>
    </div>
  </div>
</template>

<script setup lang="ts">
defineOptions({ name: 'FrontendTest' })

const currentTime = ref(new Date().toLocaleString())
const clicked = ref(false)

const handleClick = () => {
  clicked.value = true
  currentTime.value = new Date().toLocaleString()
}

// 每秒更新时间
setInterval(() => {
  currentTime.value = new Date().toLocaleString()
}, 1000)
</script>

<style scoped>
.test-page {
  padding: 40px;
  text-align: center;
}

.test-page h1 {
  color: #1890ff;
  margin-bottom: 20px;
}

.test-page p {
  margin-bottom: 20px;
  color: #666;
}

.success-message {
  margin-top: 20px;
  padding: 20px;
  background: #f0f9ff;
  border: 1px solid #1890ff;
  border-radius: 8px;
}

.success-message p {
  margin: 5px 0;
  color: #1890ff;
}
</style>
