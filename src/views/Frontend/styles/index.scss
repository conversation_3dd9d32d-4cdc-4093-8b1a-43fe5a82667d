/* 前台页面整体样式 */
.frontend-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  display: flex;
  flex-direction: column;
}

/* 页面头部样式 */
.frontend-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  font-size: 32px;
  color: #1890ff;
}

.site-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #262626;
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.login-btn {
  padding: 8px 20px;
  border-radius: 20px;
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: none;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
  transition: all 0.3s ease;
}

.login-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
}

.btn-icon {
  margin-right: 4px;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-dropdown {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.user-dropdown:hover {
  background: rgba(24, 144, 255, 0.1);
}

.user-avatar {
  border: 2px solid #1890ff;
}

.username {
  font-weight: 500;
  color: #262626;
}

.dropdown-icon {
  font-size: 12px;
  color: #8c8c8c;
}

/* 主要内容区域 */
.frontend-main {
  flex: 1;
  padding: 40px 0;
}

.main-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}



/* 查询区域 */
.query-section {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  overflow: hidden;
}

.query-container {
  padding: 0;
}

/* 搜索容器样式 */
.search-container {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  padding: 32px;
  color: white;
}

.title-icon {
  font-size: 26px;
}

.search-form {
  background: rgba(255, 255, 255, 0.98);
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.form-row {
  display: flex;
  justify-content: center;
}

.form-item-custom {
  max-width: 400px;
  width: 100%;
}

.search-input {
  width: 100%;
}

.search-btn {
  padding: 12px 32px;
  font-size: 16px;
  border-radius: 8px;
  background: #1890ff;
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
  transition: all 0.3s ease;
}

.search-btn:hover {
  background: #40a9ff;
  border-color: #40a9ff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
}

/* 批次列表头部样式 */
.batch-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

.batch-list-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.batch-list-title .title-icon {
  font-size: 18px;
  color: #1890ff;
}

.refresh-btn {
  border-radius: 6px;
  transition: all 0.3s ease;
}

.refresh-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

/* 结果容器样式 */
.result-container {
  padding: 32px;
}

/* 查询结果样式 */
.query-result {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.result-card {
  background: white;
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  text-align: center;
  max-width: 500px;
  width: 100%;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.success-card {
  border-color: #52c41a;
  background: linear-gradient(135deg, #f6ffed 0%, #ffffff 100%);
}

.result-icon-wrapper {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px;
  position: relative;
}

.result-icon-wrapper.success {
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
  box-shadow: 0 8px 24px rgba(82, 196, 26, 0.3);
}

.result-status-icon {
  font-size: 36px;
  color: white;
}

.result-content {
  text-align: center;
}

.result-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 12px 0;
  color: #262626;
}

.result-description {
  font-size: 16px;
  color: #595959;
  line-height: 1.6;
  margin: 0 0 24px 0;
}

.result-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 24px;
}

.detail-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 14px;
  color: #8c8c8c;
}

.detail-icon {
  font-size: 16px;
  color: #1890ff;
}

.import-btn {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: none;
  padding: 12px 24px;
  font-size: 16px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  transition: all 0.3s ease;
}

.import-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
}

.retry-btn {
  padding: 12px 24px;
  font-size: 16px;
  border-radius: 8px;
  color: #595959;
  border-color: #d9d9d9;
  transition: all 0.3s ease;
}

.retry-btn:hover {
  color: #1890ff;
  border-color: #1890ff;
}



/* 标签样式优化 */
:deep(.el-tag) {
  border-radius: 4px;
  font-weight: 400;
  font-size: 12px;
  height: 22px;
  line-height: 20px;
  padding: 0 7px;
}

:deep(.el-tag--info) {
  background-color: #f0f0f0;
  border-color: #d9d9d9;
  color: #595959;
}

:deep(.el-tag--warning) {
  background-color: #fff7e6;
  border-color: #ffd591;
  color: #d46b08;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-container {
    padding: 0 16px;
  }

  .site-title {
    font-size: 20px;
  }

  .main-container {
    padding: 0 16px;
  }

  .search-container {
    padding: 24px 16px;
  }

  .search-form {
    padding: 16px;
  }

  .result-container {
    padding: 20px 16px;
  }

  .result-card {
    padding: 24px;
    margin: 0 auto;
  }

  .result-icon-wrapper {
    width: 60px;
    height: 60px;
  }

  .result-status-icon {
    font-size: 28px;
  }

  .result-title {
    font-size: 20px;
  }

  .result-description {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .header-container {
    height: 56px;
  }

  .logo-icon {
    font-size: 24px;
  }

  .site-title {
    font-size: 18px;
  }

  .user-dropdown .username {
    display: none;
  }

  .result-card {
    padding: 20px;
  }

  .result-icon-wrapper {
    width: 50px;
    height: 50px;
  }

  .result-status-icon {
    font-size: 24px;
  }

  .result-title {
    font-size: 18px;
  }
}

/* 动画效果 */
.query-section {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载状态优化 */
:deep(.el-loading-mask) {
  border-radius: 8px;
  background-color: rgba(255, 255, 255, 0.9);
}

:deep(.el-loading-spinner) {
  color: #1890ff;
}

/* 数据未入库弹窗样式 */
.data-not-found-content {
  text-align: center;
  padding: 20px 0;
}

.warning-icon-wrapper {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.warning-icon {
  font-size: 48px;
  color: #faad14;
  background: linear-gradient(135deg, #fff7e6 0%, #ffeaa7 100%);
  border-radius: 50%;
  padding: 20px;
  box-shadow: 0 4px 16px rgba(250, 173, 20, 0.2);
}

.dialog-message {
  margin-bottom: 20px;
}

.message-text {
  font-size: 16px;
  color: #262626;
  margin: 0 0 8px 0;
  line-height: 1.5;
}

.message-hint {
  font-size: 14px;
  color: #8c8c8c;
  margin: 0;
  line-height: 1.4;
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 12px;
  flex-wrap: wrap;
}

.dialog-footer .cancel-btn {
  color: #8c8c8c;
  border-color: #d9d9d9;
  background: #fff;
  transition: all 0.3s ease;
}

.dialog-footer .cancel-btn:hover {
  color: #595959;
  border-color: #b7b7b7;
}

.dialog-footer .retry-btn {
  color: #1890ff;
  border-color: #1890ff;
  background: #fff;
  transition: all 0.3s ease;
}

.dialog-footer .retry-btn:hover {
  color: #40a9ff;
  border-color: #40a9ff;
}

.dialog-footer .import-btn {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: none;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
  transition: all 0.3s ease;
}

.dialog-footer .import-btn:hover {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
}

/* 弹窗标题样式优化 */
:deep(.el-dialog__header) {
  padding: 20px 20px 0 20px;
  text-align: center;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 600;
  color: #262626;
}

:deep(.el-dialog__body) {
  padding: 10px 20px 20px 20px;
}

/* 响应式弹窗样式 */
@media (max-width: 768px) {
  .dialog-footer {
    flex-direction: column;
    align-items: stretch;
  }

  .dialog-footer .el-button {
    width: 100%;
    margin: 0 0 8px 0;
  }

  .warning-icon {
    font-size: 40px;
    padding: 16px;
  }

  .message-text {
    font-size: 15px;
  }

  .message-hint {
    font-size: 13px;
  }
}
